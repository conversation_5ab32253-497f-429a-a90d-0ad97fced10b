/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/client/dashboard/page";
exports.ids = ["app/client/dashboard/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fclient%2Fdashboard%2Fpage&page=%2Fclient%2Fdashboard%2Fpage&appPaths=%2Fclient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fclient%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fclient%2Fdashboard%2Fpage&page=%2Fclient%2Fdashboard%2Fpage&appPaths=%2Fclient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fclient%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'client',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client/dashboard/page.tsx */ \"(rsc)/./src/app/client/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/client/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/client/dashboard/page\",\n        pathname: \"/client/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fclient%2Fdashboard%2Fpage&page=%2Fclient%2Fdashboard%2Fpage&appPaths=%2Fclient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fclient%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp%5Cclient%5Cdashboard%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp%5Cclient%5Cdashboard%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client/dashboard/page.tsx */ \"(ssr)/./src/app/client/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDcGVyc29uYWwlNUNEZXNrdG9wJTVDbmV3JTIwZGFzaGJvYXJkJTVDc3JjJTVDYXBwJTVDY2xpZW50JTVDZGFzaGJvYXJkJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVzaWduLWFnZW5jeS1kYXNoYm9hcmQvPzM0YjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZXJzb25hbFxcXFxEZXNrdG9wXFxcXG5ldyBkYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxjbGllbnRcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp%5Cclient%5Cdashboard%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/client/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/client/dashboard/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Modal */ \"(ssr)/./src/components/Modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ClientDashboard() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const [showNewProjectModal, setShowNewProjectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewTicketModal, setShowNewTicketModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfileEdit, setShowProfileEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Handle logout\n    const handleLogout = ()=>{\n        if (confirm(\"هل أنت متأكد من تسجيل الخروج؟\")) {\n            router.push(\"/\");\n        }\n    };\n    // Handle new project\n    const handleNewProject = ()=>{\n        setShowNewProjectModal(true);\n    };\n    // Handle new support ticket\n    const handleNewTicket = ()=>{\n        setShowNewTicketModal(true);\n    };\n    // Handle project details\n    const handleProjectDetails = (projectId)=>{\n        alert(`عرض تفاصيل المشروع رقم ${projectId}`);\n    };\n    // Handle file download\n    const handleDownloadFiles = (projectId)=>{\n        alert(`تحميل ملفات المشروع رقم ${projectId}`);\n    };\n    // Handle view ticket\n    const handleViewTicket = (ticketId)=>{\n        alert(`عرض تذكرة الدعم رقم ${ticketId}`);\n    };\n    // Handle save profile\n    const handleSaveProfile = ()=>{\n        alert(\"تم حفظ التغييرات بنجاح!\");\n        setShowProfileEdit(false);\n    };\n    const projects = [\n        {\n            id: 1,\n            name: \"تصميم موقع الشركة\",\n            status: \"قيد التنفيذ\",\n            progress: 75,\n            dueDate: \"2024-01-15\"\n        },\n        {\n            id: 2,\n            name: \"تصميم الهوية البصرية\",\n            status: \"مكتمل\",\n            progress: 100,\n            dueDate: \"2024-01-10\"\n        },\n        {\n            id: 3,\n            name: \"تطبيق الجوال\",\n            status: \"في الانتظار\",\n            progress: 25,\n            dueDate: \"2024-02-01\"\n        }\n    ];\n    const supportTickets = [\n        {\n            id: 1,\n            subject: \"طلب تعديل على التصميم\",\n            status: \"مفتوح\",\n            priority: \"عالي\",\n            date: \"2024-01-12\"\n        },\n        {\n            id: 2,\n            subject: \"استفسار عن الموعد النهائي\",\n            status: \"مغلق\",\n            priority: \"متوسط\",\n            date: \"2024-01-10\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"مكتمل\":\n                return \"text-green-600 bg-green-100\";\n            case \"قيد التنفيذ\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"في الانتظار\":\n                return \"text-yellow-600 bg-yellow-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"عالي\":\n                return \"text-red-600 bg-red-100\";\n            case \"متوسط\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"منخفض\":\n                return \"text-green-600 bg-green-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-white shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-bold text-gray-800\",\n                                            children: \"وكالة التصميم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"لوحة العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"dashboard\"),\n                                    className: `w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${activeTab === \"dashboard\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"projects\"),\n                                    className: `w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${activeTab === \"projects\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"مشاريعي\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"support\"),\n                                    className: `w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${activeTab === \"support\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الدعم الفني\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"settings\"),\n                                    className: `w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${activeTab === \"settings\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 right-4 left-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"w-full flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5 ml-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                \"تسجيل الخروج\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-800\",\n                                        children: \"مرحباً، أحمد محمد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"إليك نظرة عامة على مشاريعك\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"dashboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"إجمالي المشاريع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-800\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-8 h-8 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"قيد التنفيذ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"مكتملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"المشاريع الحديثة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: projects.slice(0, 2).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800\",\n                                                                    children: project.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2 space-x-4 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`,\n                                                                            children: project.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"موعد التسليم: \",\n                                                                                project.dueDate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-primary-600 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: `${project.progress}%`\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 mt-1 text-center\",\n                                                                    children: [\n                                                                        project.progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, project.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"projects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"مشاريعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNewProject,\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مشروع جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6\",\n                                children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: project.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    \"موعد التسليم: \",\n                                                                    project.dueDate\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`,\n                                                        children: project.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"التقدم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    project.progress,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-200 rounded-full h-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                                            style: {\n                                                                width: `${project.progress}%`\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleProjectDetails(project.id),\n                                                        className: \"btn-primary text-sm\",\n                                                        children: \"عرض التفاصيل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDownloadFiles(project.id),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: \"تحميل الملفات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"support\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"الدعم الفني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNewTicket,\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تذكرة جديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: supportTickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-800\",\n                                                                children: ticket.subject\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mt-2 space-x-4 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${ticket.status === \"مفتوح\" ? \"text-green-600 bg-green-100\" : \"text-gray-600 bg-gray-100\"}`,\n                                                                        children: ticket.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`,\n                                                                        children: ticket.priority\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: ticket.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewTicket(ticket.id),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: \"عرض\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, ticket.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"الإعدادات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"المعلومات الشخصية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الاسم الكامل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"أحمد محمد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"البريد الإلكتروني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"رقم الهاتف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"+966 50 123 4567\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الشركة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"شركة التقنية المتقدمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSaveProfile,\n                                            className: \"btn-primary\",\n                                            children: \"حفظ التغييرات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showNewProjectModal,\n                onClose: ()=>setShowNewProjectModal(false),\n                title: \"طلب مشروع جديد\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"اسم المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل اسم المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"نوع المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تصميم موقع ويب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تصميم هوية بصرية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تطبيق جوال\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تصميم جرافيك\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"وصف المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    rows: 4,\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"اكتب وصفاً مفصلاً للمشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الميزانية المتوقعة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل الميزانية بالريال\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 space-x-reverse pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        alert(\"تم إرسال طلب المشروع بنجاح!\");\n                                        setShowNewProjectModal(false);\n                                    },\n                                    className: \"btn-primary flex-1\",\n                                    children: \"إرسال الطلب\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowNewProjectModal(false),\n                                    className: \"btn-secondary flex-1\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showNewTicketModal,\n                onClose: ()=>setShowNewTicketModal(false),\n                title: \"تذكرة دعم جديدة\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"موضوع التذكرة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل موضوع التذكرة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الأولوية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"منخفض\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"متوسط\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"عالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الوصف\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    rows: 5,\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"اكتب وصفاً مفصلاً للمشكلة أو الاستفسار\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 space-x-reverse pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        alert(\"تم إرسال تذكرة الدعم بنجاح!\");\n                                        setShowNewTicketModal(false);\n                                    },\n                                    className: \"btn-primary flex-1\",\n                                    children: \"إرسال التذكرة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowNewTicketModal(false),\n                                    className: \"btn-secondary flex-1\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/client/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Modal.tsx":
/*!**********************************!*\
  !*** ./src/components/Modal.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Modal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Modal({ isOpen, onClose, title, children }) {\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"w-5 h-5 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\components\\\\Modal.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Modal.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"346df53da3b9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVzaWduLWFnZW5jeS1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzM3ZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNDZkZjUzZGEzYjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/client/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/client/dashboard/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\new dashboard\src\app\client\dashboard\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Design Agency Dashboard\",\n    description: \"Professional dashboard for design agency clients and administrators\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLEtBQUk7a0JBQ2xCLDRFQUFDQztZQUFLQyxXQUFXViwrSkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVzaWduLWFnZW5jeS1kYXNoYm9hcmQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0Rlc2lnbiBBZ2VuY3kgRGFzaGJvYXJkJyxcbiAgZGVzY3JpcHRpb246ICdQcm9mZXNzaW9uYWwgZGFzaGJvYXJkIGZvciBkZXNpZ24gYWdlbmN5IGNsaWVudHMgYW5kIGFkbWluaXN0cmF0b3JzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImFyXCIgZGlyPVwicnRsXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiZGlyIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fclient%2Fdashboard%2Fpage&page=%2Fclient%2Fdashboard%2Fpage&appPaths=%2Fclient%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fclient%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpersonal%5CDesktop%5Cnew%20dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();