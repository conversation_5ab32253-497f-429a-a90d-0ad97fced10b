"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin/dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin/dashboard/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DollarSign,FileText,Filter,LayoutDashboard,LogOut,MessageCircle,MoreVertical,Palette,Plus,Search,Settings,Shield,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showNewClientModal, setShowNewClientModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewProjectModal, setShowNewProjectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Handle logout\n    const handleLogout = ()=>{\n        if (confirm(\"هل أنت متأكد من تسجيل الخروج؟\")) {\n            router.push(\"/\");\n        }\n    };\n    // Handle new client\n    const handleNewClient = ()=>{\n        setShowNewClientModal(true);\n    };\n    // Handle new project\n    const handleNewProject = ()=>{\n        setShowNewProjectModal(true);\n    };\n    // Handle edit client\n    const handleEditClient = (clientId)=>{\n        alert(\"تعديل العميل رقم \".concat(clientId));\n    };\n    // Handle delete client\n    const handleDeleteClient = (clientId)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا العميل؟\")) {\n            alert(\"تم حذف العميل رقم \".concat(clientId));\n        }\n    };\n    // Handle edit project\n    const handleEditProject = (projectId)=>{\n        alert(\"تعديل المشروع رقم \".concat(projectId));\n    };\n    // Handle project details\n    const handleProjectDetails = (projectId)=>{\n        alert(\"عرض تفاصيل المشروع رقم \".concat(projectId));\n    };\n    // Handle reply to ticket\n    const handleReplyTicket = (ticketId)=>{\n        alert(\"الرد على تذكرة رقم \".concat(ticketId));\n    };\n    // Handle close ticket\n    const handleCloseTicket = (ticketId)=>{\n        if (confirm(\"هل أنت متأكد من إغلاق هذه التذكرة؟\")) {\n            alert(\"تم إغلاق التذكرة رقم \".concat(ticketId));\n        }\n    };\n    // Handle save settings\n    const handleSaveSettings = ()=>{\n        alert(\"تم حفظ الإعدادات بنجاح!\");\n    };\n    const stats = {\n        totalClients: 24,\n        activeProjects: 12,\n        completedProjects: 45,\n        revenue: 125000\n    };\n    const clients = [\n        {\n            id: 1,\n            name: \"أحمد محمد\",\n            email: \"<EMAIL>\",\n            projects: 3,\n            status: \"نشط\",\n            joinDate: \"2024-01-01\"\n        },\n        {\n            id: 2,\n            name: \"فاطمة علي\",\n            email: \"<EMAIL>\",\n            projects: 2,\n            status: \"نشط\",\n            joinDate: \"2024-01-05\"\n        },\n        {\n            id: 3,\n            name: \"محمد سالم\",\n            email: \"<EMAIL>\",\n            projects: 1,\n            status: \"غير نشط\",\n            joinDate: \"2024-01-10\"\n        }\n    ];\n    // Filter clients based on search term\n    const filteredClients = clients.filter((client)=>client.name.toLowerCase().includes(searchTerm.toLowerCase()) || client.email.toLowerCase().includes(searchTerm.toLowerCase()));\n    const projects = [\n        {\n            id: 1,\n            name: \"تصميم موقع الشركة\",\n            client: \"أحمد محمد\",\n            status: \"قيد التنفيذ\",\n            progress: 75,\n            dueDate: \"2024-01-15\",\n            budget: 15000\n        },\n        {\n            id: 2,\n            name: \"تصميم الهوية البصرية\",\n            client: \"فاطمة علي\",\n            status: \"مكتمل\",\n            progress: 100,\n            dueDate: \"2024-01-10\",\n            budget: 8000\n        },\n        {\n            id: 3,\n            name: \"تطبيق الجوال\",\n            client: \"محمد سالم\",\n            status: \"في الانتظار\",\n            progress: 25,\n            dueDate: \"2024-02-01\",\n            budget: 25000\n        }\n    ];\n    const supportTickets = [\n        {\n            id: 1,\n            client: \"أحمد محمد\",\n            subject: \"طلب تعديل على التصميم\",\n            status: \"مفتوح\",\n            priority: \"عالي\",\n            date: \"2024-01-12\"\n        },\n        {\n            id: 2,\n            client: \"فاطمة علي\",\n            subject: \"استفسار عن الموعد النهائي\",\n            status: \"قيد المراجعة\",\n            priority: \"متوسط\",\n            date: \"2024-01-11\"\n        },\n        {\n            id: 3,\n            client: \"محمد سالم\",\n            subject: \"مشكلة في الوصول للملفات\",\n            status: \"مغلق\",\n            priority: \"منخفض\",\n            date: \"2024-01-10\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"مكتمل\":\n                return \"text-green-600 bg-green-100\";\n            case \"قيد التنفيذ\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"في الانتظار\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"نشط\":\n                return \"text-green-600 bg-green-100\";\n            case \"غير نشط\":\n                return \"text-gray-600 bg-gray-100\";\n            case \"مفتوح\":\n                return \"text-red-600 bg-red-100\";\n            case \"قيد المراجعة\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"مغلق\":\n                return \"text-gray-600 bg-gray-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"عالي\":\n                return \"text-red-600 bg-red-100\";\n            case \"متوسط\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"منخفض\":\n                return \"text-green-600 bg-green-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-white shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-bold text-gray-800\",\n                                            children: \"وكالة التصميم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"لوحة الإدارة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"dashboard\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"dashboard\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"clients\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"clients\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"العملاء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"projects\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"projects\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"المشاريع\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"support\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"support\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الدعم الفني\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"settings\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"settings\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 right-4 left-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"w-full flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-5 h-5 ml-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                \"تسجيل الخروج\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-800\",\n                                        children: \"مرحباً، مدير النظام\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"إليك نظرة عامة على أداء الوكالة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"dashboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"إجمالي العملاء\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-800\",\n                                                            children: stats.totalClients\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-8 h-8 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"المشاريع النشطة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: stats.activeProjects\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"المشاريع المكتملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: stats.completedProjects\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"الإيرادات (ريال)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: stats.revenue.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-800\",\n                                                    children: \"المشاريع الحديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: projects.slice(0, 3).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-gray-800\",\n                                                                            children: project.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: project.client\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(project.status)),\n                                                                    children: project.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, project.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-800\",\n                                                    children: \"تذاكر الدعم الحديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: supportTickets.slice(0, 3).map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium text-gray-800\",\n                                                                            children: ticket.subject\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: ticket.client\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(ticket.status)),\n                                                                            children: ticket.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(ticket.priority)),\n                                                                            children: ticket.priority\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, ticket.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"clients\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"إدارة العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNewClient,\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"عميل جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-xl shadow-sm border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث عن عميل...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"تصفية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"المشاريع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"تاريخ الانضمام\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"إجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: filteredClients.length > 0 ? filteredClients.map((client)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: client.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: client.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: client.projects\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(client.status)),\n                                                                children: client.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: client.joinDate\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleEditClient(client.id),\n                                                                    className: \"text-primary-600 hover:text-primary-900 ml-4\",\n                                                                    children: \"تعديل\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteClient(client.id),\n                                                                    className: \"text-red-600 hover:text-red-900\",\n                                                                    children: \"حذف\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, client.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    colSpan: 5,\n                                                    className: \"px-6 py-8 text-center text-gray-500\",\n                                                    children: \"لا توجد عملاء مطابقين لبحثك\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"projects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"إدارة المشاريع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNewProject,\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مشروع جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6\",\n                                children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: project.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    \"العميل: \",\n                                                                    project.client\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    \"الميزانية: \",\n                                                                    project.budget.toLocaleString(),\n                                                                    \" ريال\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(project.status)),\n                                                                children: project.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-2 hover:bg-gray-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DollarSign_FileText_Filter_LayoutDashboard_LogOut_MessageCircle_MoreVertical_Palette_Plus_Search_Settings_Shield_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"التقدم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    project.progress,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-200 rounded-full h-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                                            style: {\n                                                                width: \"\".concat(project.progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"موعد التسليم: \",\n                                                            project.dueDate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEditProject(project.id),\n                                                                className: \"btn-primary text-sm\",\n                                                                children: \"تعديل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleProjectDetails(project.id),\n                                                                className: \"btn-secondary text-sm\",\n                                                                children: \"عرض التفاصيل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"support\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800\",\n                                    children: \"إدارة الدعم الفني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الموضوع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"الأولوية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"التاريخ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                        children: \"إجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white divide-y divide-gray-200\",\n                                            children: supportTickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                            children: ticket.subject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: ticket.client\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(ticket.status)),\n                                                                children: ticket.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(ticket.priority)),\n                                                                children: ticket.priority\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                            children: ticket.date\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleReplyTicket(ticket.id),\n                                                                    className: \"text-primary-600 hover:text-primary-900 ml-4\",\n                                                                    children: \"رد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleCloseTicket(ticket.id),\n                                                                    className: \"text-green-600 hover:text-green-900\",\n                                                                    children: \"إغلاق\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, ticket.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"إعدادات النظام\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"إعدادات عامة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"اسم الوكالة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                                defaultValue: \"وكالة التصميم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"البريد الإلكتروني\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                                defaultValue: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"رقم الهاتف\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                                defaultValue: \"+966 11 123 4567\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-sm border p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                                children: \"إعدادات الأمان\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\",\n                                                                    defaultChecked: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 text-sm text-gray-700\",\n                                                                    children: \"تفعيل المصادقة الثنائية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\",\n                                                                    defaultChecked: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 text-sm text-gray-700\",\n                                                                    children: \"إرسال تنبيهات الأمان\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-2 text-sm text-gray-700\",\n                                                                    children: \"تسجيل جميع العمليات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSaveSettings,\n                                    className: \"btn-primary\",\n                                    children: \"حفظ جميع الإعدادات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showNewClientModal,\n                onClose: ()=>setShowNewClientModal(false),\n                title: \"إضافة عميل جديد\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الاسم الكامل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل الاسم الكامل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"البريد الإلكتروني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل البريد الإلكتروني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"رقم الهاتف\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"tel\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل رقم الهاتف\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الشركة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل اسم الشركة (اختياري)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 space-x-reverse pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        alert(\"تم إضافة العميل بنجاح!\");\n                                        setShowNewClientModal(false);\n                                    },\n                                    className: \"btn-primary flex-1\",\n                                    children: \"إضافة العميل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowNewClientModal(false),\n                                    className: \"btn-secondary flex-1\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 608,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showNewProjectModal,\n                onClose: ()=>setShowNewProjectModal(false),\n                title: \"إضافة مشروع جديد\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"اسم المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل اسم المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"العميل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"أحمد محمد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"فاطمة علي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"محمد سالم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"نوع المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تصميم موقع ويب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تصميم هوية بصرية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تطبيق جوال\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            children: \"تصميم جرافيك\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الميزانية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"أدخل الميزانية بالريال\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"تاريخ التسليم\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 space-x-reverse pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        alert(\"تم إضافة المشروع بنجاح!\");\n                                        setShowNewProjectModal(false);\n                                    },\n                                    className: \"btn-primary flex-1\",\n                                    children: \"إضافة المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setShowNewProjectModal(false),\n                                    className: \"btn-secondary flex-1\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 669,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"fR6EOIhJmg2EJC2C86sL6SdObmc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/dashboard/page.tsx\n"));

/***/ })

});