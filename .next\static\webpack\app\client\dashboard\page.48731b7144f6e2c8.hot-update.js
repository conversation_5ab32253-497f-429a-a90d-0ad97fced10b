"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/client/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/client/dashboard/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/client/dashboard/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ClientDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,LayoutDashboard,LogOut,MessageCircle,Palette,Plus,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ClientDashboard() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const [showNewProjectModal, setShowNewProjectModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewTicketModal, setShowNewTicketModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showProfileEdit, setShowProfileEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Handle logout\n    const handleLogout = ()=>{\n        if (confirm(\"هل أنت متأكد من تسجيل الخروج؟\")) {\n            router.push(\"/\");\n        }\n    };\n    // Handle new project\n    const handleNewProject = ()=>{\n        setShowNewProjectModal(true);\n    };\n    // Handle new support ticket\n    const handleNewTicket = ()=>{\n        setShowNewTicketModal(true);\n    };\n    // Handle project details\n    const handleProjectDetails = (projectId)=>{\n        alert(\"عرض تفاصيل المشروع رقم \".concat(projectId));\n    };\n    // Handle file download\n    const handleDownloadFiles = (projectId)=>{\n        alert(\"تحميل ملفات المشروع رقم \".concat(projectId));\n    };\n    // Handle view ticket\n    const handleViewTicket = (ticketId)=>{\n        alert(\"عرض تذكرة الدعم رقم \".concat(ticketId));\n    };\n    // Handle save profile\n    const handleSaveProfile = ()=>{\n        alert(\"تم حفظ التغييرات بنجاح!\");\n        setShowProfileEdit(false);\n    };\n    const projects = [\n        {\n            id: 1,\n            name: \"تصميم موقع الشركة\",\n            status: \"قيد التنفيذ\",\n            progress: 75,\n            dueDate: \"2024-01-15\"\n        },\n        {\n            id: 2,\n            name: \"تصميم الهوية البصرية\",\n            status: \"مكتمل\",\n            progress: 100,\n            dueDate: \"2024-01-10\"\n        },\n        {\n            id: 3,\n            name: \"تطبيق الجوال\",\n            status: \"في الانتظار\",\n            progress: 25,\n            dueDate: \"2024-02-01\"\n        }\n    ];\n    const supportTickets = [\n        {\n            id: 1,\n            subject: \"طلب تعديل على التصميم\",\n            status: \"مفتوح\",\n            priority: \"عالي\",\n            date: \"2024-01-12\"\n        },\n        {\n            id: 2,\n            subject: \"استفسار عن الموعد النهائي\",\n            status: \"مغلق\",\n            priority: \"متوسط\",\n            date: \"2024-01-10\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"مكتمل\":\n                return \"text-green-600 bg-green-100\";\n            case \"قيد التنفيذ\":\n                return \"text-blue-600 bg-blue-100\";\n            case \"في الانتظار\":\n                return \"text-yellow-600 bg-yellow-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"عالي\":\n                return \"text-red-600 bg-red-100\";\n            case \"متوسط\":\n                return \"text-yellow-600 bg-yellow-100\";\n            case \"منخفض\":\n                return \"text-green-600 bg-green-100\";\n            default:\n                return \"text-gray-600 bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-white shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-bold text-gray-800\",\n                                            children: \"وكالة التصميم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"لوحة العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"dashboard\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"dashboard\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الرئيسية\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"projects\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"projects\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"مشاريعي\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"support\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"support\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الدعم الفني\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(\"settings\"),\n                                    className: \"w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors \".concat(activeTab === \"settings\" ? \"bg-primary-50 text-primary-600\" : \"text-gray-600 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"الإعدادات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-4 right-4 left-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"w-full flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 ml-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                \"تسجيل الخروج\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-800\",\n                                        children: \"مرحباً، أحمد محمد\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"إليك نظرة عامة على مشاريعك\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === \"dashboard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"إجمالي المشاريع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-800\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-8 h-8 text-primary-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"قيد التنفيذ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"مكتملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border-b\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800\",\n                                            children: \"المشاريع الحديثة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: projects.slice(0, 2).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800\",\n                                                                    children: project.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mt-2 space-x-4 space-x-reverse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(project.status)),\n                                                                            children: project.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: [\n                                                                                \"موعد التسليم: \",\n                                                                                project.dueDate\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-200 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-primary-600 h-2 rounded-full\",\n                                                                        style: {\n                                                                            width: \"\".concat(project.progress, \"%\")\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 mt-1 text-center\",\n                                                                    children: [\n                                                                        project.progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, project.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"projects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"مشاريعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNewProject,\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مشروع جديد\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-6\",\n                                children: projects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white p-6 rounded-xl shadow-sm border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-800\",\n                                                                children: project.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600\",\n                                                                children: [\n                                                                    \"موعد التسليم: \",\n                                                                    project.dueDate\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(project.status)),\n                                                        children: project.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"التقدم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    project.progress,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-200 rounded-full h-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                                            style: {\n                                                                width: \"\".concat(project.progress, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleProjectDetails(project.id),\n                                                        className: \"btn-primary text-sm\",\n                                                        children: \"عرض التفاصيل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDownloadFiles(project.id),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: \"تحميل الملفات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, project.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"support\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-800\",\n                                        children: \"الدعم الفني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleNewTicket,\n                                        className: \"btn-primary flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_LayoutDashboard_LogOut_MessageCircle_Palette_Plus_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تذكرة جديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: supportTickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-800\",\n                                                                children: ticket.subject\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mt-2 space-x-4 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(ticket.status === \"مفتوح\" ? \"text-green-600 bg-green-100\" : \"text-gray-600 bg-gray-100\"),\n                                                                        children: ticket.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getPriorityColor(ticket.priority)),\n                                                                        children: ticket.priority\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: ticket.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewTicket(ticket.id),\n                                                        className: \"btn-secondary text-sm\",\n                                                        children: \"عرض\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, ticket.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"الإعدادات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-sm border p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                        children: \"المعلومات الشخصية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الاسم الكامل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"أحمد محمد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"البريد الإلكتروني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"رقم الهاتف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"+966 50 123 4567\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"الشركة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"w-full p-3 border border-gray-300 rounded-lg\",\n                                                        defaultValue: \"شركة التقنية المتقدمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSaveProfile,\n                                            className: \"btn-primary\",\n                                            children: \"حفظ التغييرات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\new dashboard\\\\src\\\\app\\\\client\\\\dashboard\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientDashboard, \"iKI8U2qNrUiM5DOtHRitJVxASMU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ClientDashboard;\nvar _c;\n$RefreshReg$(_c, \"ClientDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/client/dashboard/page.tsx\n"));

/***/ })

});