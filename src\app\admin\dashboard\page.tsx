'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Modal from '@/components/Modal'
import {
  LayoutDashboard,
  Users,
  FileText,
  MessageCircle,
  Settings,
  LogOut,
  Plus,
  TrendingUp,
  DollarSign,
  Calendar,
  Search,
  Filter,
  MoreVertical,
  Palette,
  Shield
} from 'lucide-react'

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [searchTerm, setSearchTerm] = useState('')
  const [showNewClientModal, setShowNewClientModal] = useState(false)
  const [showNewProjectModal, setShowNewProjectModal] = useState(false)
  const router = useRouter()

  // Handle logout
  const handleLogout = () => {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      router.push('/')
    }
  }

  // Handle new client
  const handleNewClient = () => {
    setShowNewClientModal(true)
  }

  // Handle new project
  const handleNewProject = () => {
    setShowNewProjectModal(true)
  }

  // Handle edit client
  const handleEditClient = (clientId: number) => {
    alert(`تعديل العميل رقم ${clientId}`)
  }

  // Handle delete client
  const handleDeleteClient = (clientId: number) => {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      alert(`تم حذف العميل رقم ${clientId}`)
    }
  }

  // Handle edit project
  const handleEditProject = (projectId: number) => {
    alert(`تعديل المشروع رقم ${projectId}`)
  }

  // Handle project details
  const handleProjectDetails = (projectId: number) => {
    alert(`عرض تفاصيل المشروع رقم ${projectId}`)
  }

  // Handle reply to ticket
  const handleReplyTicket = (ticketId: number) => {
    alert(`الرد على تذكرة رقم ${ticketId}`)
  }

  // Handle close ticket
  const handleCloseTicket = (ticketId: number) => {
    if (confirm('هل أنت متأكد من إغلاق هذه التذكرة؟')) {
      alert(`تم إغلاق التذكرة رقم ${ticketId}`)
    }
  }

  // Handle save settings
  const handleSaveSettings = () => {
    alert('تم حفظ الإعدادات بنجاح!')
  }

  const stats = {
    totalClients: 24,
    activeProjects: 12,
    completedProjects: 45,
    revenue: 125000
  }

  const clients = [
    { id: 1, name: 'أحمد محمد', email: '<EMAIL>', projects: 3, status: 'نشط', joinDate: '2024-01-01' },
    { id: 2, name: 'فاطمة علي', email: '<EMAIL>', projects: 2, status: 'نشط', joinDate: '2024-01-05' },
    { id: 3, name: 'محمد سالم', email: '<EMAIL>', projects: 1, status: 'غير نشط', joinDate: '2024-01-10' },
  ]

  // Filter clients based on search term
  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const projects = [
    { id: 1, name: 'تصميم موقع الشركة', client: 'أحمد محمد', status: 'قيد التنفيذ', progress: 75, dueDate: '2024-01-15', budget: 15000 },
    { id: 2, name: 'تصميم الهوية البصرية', client: 'فاطمة علي', status: 'مكتمل', progress: 100, dueDate: '2024-01-10', budget: 8000 },
    { id: 3, name: 'تطبيق الجوال', client: 'محمد سالم', status: 'في الانتظار', progress: 25, dueDate: '2024-02-01', budget: 25000 },
  ]

  const supportTickets = [
    { id: 1, client: 'أحمد محمد', subject: 'طلب تعديل على التصميم', status: 'مفتوح', priority: 'عالي', date: '2024-01-12' },
    { id: 2, client: 'فاطمة علي', subject: 'استفسار عن الموعد النهائي', status: 'قيد المراجعة', priority: 'متوسط', date: '2024-01-11' },
    { id: 3, client: 'محمد سالم', subject: 'مشكلة في الوصول للملفات', status: 'مغلق', priority: 'منخفض', date: '2024-01-10' },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل': return 'text-green-600 bg-green-100'
      case 'قيد التنفيذ': return 'text-blue-600 bg-blue-100'
      case 'في الانتظار': return 'text-yellow-600 bg-yellow-100'
      case 'نشط': return 'text-green-600 bg-green-100'
      case 'غير نشط': return 'text-gray-600 bg-gray-100'
      case 'مفتوح': return 'text-red-600 bg-red-100'
      case 'قيد المراجعة': return 'text-yellow-600 bg-yellow-100'
      case 'مغلق': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالي': return 'text-red-600 bg-red-100'
      case 'متوسط': return 'text-yellow-600 bg-yellow-100'
      case 'منخفض': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex" dir="rtl">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <Palette className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-gray-800">وكالة التصميم</h2>
              <p className="text-sm text-gray-600">لوحة الإدارة</p>
            </div>
          </div>
        </div>

        <nav className="mt-6">
          <div className="px-4 space-y-2">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'dashboard' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <LayoutDashboard className="w-5 h-5 ml-3" />
              الرئيسية
            </button>
            <button
              onClick={() => setActiveTab('clients')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'clients' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Users className="w-5 h-5 ml-3" />
              العملاء
            </button>
            <button
              onClick={() => setActiveTab('projects')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'projects' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <FileText className="w-5 h-5 ml-3" />
              المشاريع
            </button>
            <button
              onClick={() => setActiveTab('support')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'support' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <MessageCircle className="w-5 h-5 ml-3" />
              الدعم الفني
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'settings' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Settings className="w-5 h-5 ml-3" />
              الإعدادات
            </button>
          </div>
        </nav>

        <div className="absolute bottom-4 right-4 left-4">
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
          >
            <LogOut className="w-5 h-5 ml-3" />
            تسجيل الخروج
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">مرحباً، مدير النظام</h1>
            <p className="text-gray-600">إليك نظرة عامة على أداء الوكالة</p>
          </div>
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
              <Shield className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        {/* Dashboard Content */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">إجمالي العملاء</p>
                    <p className="text-2xl font-bold text-gray-800">{stats.totalClients}</p>
                  </div>
                  <Users className="w-8 h-8 text-primary-600" />
                </div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">المشاريع النشطة</p>
                    <p className="text-2xl font-bold text-blue-600">{stats.activeProjects}</p>
                  </div>
                  <FileText className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">المشاريع المكتملة</p>
                    <p className="text-2xl font-bold text-green-600">{stats.completedProjects}</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">الإيرادات (ريال)</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.revenue.toLocaleString()}</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-600" />
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Projects */}
              <div className="bg-white rounded-xl shadow-sm border">
                <div className="p-6 border-b">
                  <h2 className="text-xl font-semibold text-gray-800">المشاريع الحديثة</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {projects.slice(0, 3).map((project) => (
                      <div key={project.id} className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium text-gray-800">{project.name}</h3>
                          <p className="text-sm text-gray-600">{project.client}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Recent Support Tickets */}
              <div className="bg-white rounded-xl shadow-sm border">
                <div className="p-6 border-b">
                  <h2 className="text-xl font-semibold text-gray-800">تذاكر الدعم الحديثة</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {supportTickets.slice(0, 3).map((ticket) => (
                      <div key={ticket.id} className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium text-gray-800">{ticket.subject}</h3>
                          <p className="text-sm text-gray-600">{ticket.client}</p>
                        </div>
                        <div className="flex space-x-2 space-x-reverse">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                            {ticket.status}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Clients Tab */}
        {activeTab === 'clients' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-800">إدارة العملاء</h2>
              <button
                onClick={handleNewClient}
                className="btn-primary flex items-center"
              >
                <Plus className="w-4 h-4 ml-2" />
                عميل جديد
              </button>
            </div>

            {/* Search and Filter */}
            <div className="bg-white p-4 rounded-xl shadow-sm border">
              <div className="flex space-x-4 space-x-reverse">
                <div className="flex-1 relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="البحث عن عميل..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
                <button className="btn-secondary flex items-center">
                  <Filter className="w-4 h-4 ml-2" />
                  تصفية
                </button>
              </div>
            </div>

            {/* Clients Table */}
            <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المشاريع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الانضمام</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredClients.length > 0 ? (
                    filteredClients.map((client) => (
                      <tr key={client.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{client.name}</div>
                            <div className="text-sm text-gray-500">{client.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{client.projects}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(client.status)}`}>
                            {client.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{client.joinDate}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => handleEditClient(client.id)}
                            className="text-primary-600 hover:text-primary-900 ml-4"
                          >
                            تعديل
                          </button>
                          <button
                            onClick={() => handleDeleteClient(client.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            حذف
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                        لا توجد عملاء مطابقين لبحثك
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Projects Tab */}
        {activeTab === 'projects' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-800">إدارة المشاريع</h2>
              <button
                onClick={handleNewProject}
                className="btn-primary flex items-center"
              >
                <Plus className="w-4 h-4 ml-2" />
                مشروع جديد
              </button>
            </div>

            <div className="grid gap-6">
              {projects.map((project) => (
                <div key={project.id} className="bg-white p-6 rounded-xl shadow-sm border">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{project.name}</h3>
                      <p className="text-gray-600">العميل: {project.client}</p>
                      <p className="text-gray-600">الميزانية: {project.budget.toLocaleString()} ريال</p>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
                        {project.status}
                      </span>
                      <button className="p-2 hover:bg-gray-100 rounded-lg">
                        <MoreVertical className="w-4 h-4 text-gray-600" />
                      </button>
                    </div>
                  </div>
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>التقدم</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">موعد التسليم: {project.dueDate}</span>
                    <div className="flex space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleEditProject(project.id)}
                        className="btn-primary text-sm"
                      >
                        تعديل
                      </button>
                      <button
                        onClick={() => handleProjectDetails(project.id)}
                        className="btn-secondary text-sm"
                      >
                        عرض التفاصيل
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Support Tab */}
        {activeTab === 'support' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-800">إدارة الدعم الفني</h2>
            </div>

            <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموضوع</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأولوية</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {supportTickets.map((ticket) => (
                    <tr key={ticket.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{ticket.subject}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{ticket.client}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                          {ticket.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{ticket.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleReplyTicket(ticket.id)}
                          className="text-primary-600 hover:text-primary-900 ml-4"
                        >
                          رد
                        </button>
                        <button
                          onClick={() => handleCloseTicket(ticket.id)}
                          className="text-green-600 hover:text-green-900"
                        >
                          إغلاق
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">إعدادات النظام</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">إعدادات عامة</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اسم الوكالة</label>
                    <input type="text" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="وكالة التصميم" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="<EMAIL>" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input type="tel" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="+966 11 123 4567" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">إعدادات الأمان</h3>
                <div className="space-y-4">
                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" defaultChecked />
                      <span className="mr-2 text-sm text-gray-700">تفعيل المصادقة الثنائية</span>
                    </label>
                  </div>
                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" defaultChecked />
                      <span className="mr-2 text-sm text-gray-700">إرسال تنبيهات الأمان</span>
                    </label>
                  </div>
                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                      <span className="mr-2 text-sm text-gray-700">تسجيل جميع العمليات</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveSettings}
                className="btn-primary"
              >
                حفظ جميع الإعدادات
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <Modal
        isOpen={showNewClientModal}
        onClose={() => setShowNewClientModal(false)}
        title="إضافة عميل جديد"
      >
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل الاسم الكامل"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
            <input
              type="email"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل البريد الإلكتروني"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
            <input
              type="tel"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل رقم الهاتف"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الشركة</label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل اسم الشركة (اختياري)"
            />
          </div>
          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              onClick={(e) => {
                e.preventDefault()
                alert('تم إضافة العميل بنجاح!')
                setShowNewClientModal(false)
              }}
              className="btn-primary flex-1"
            >
              إضافة العميل
            </button>
            <button
              type="button"
              onClick={() => setShowNewClientModal(false)}
              className="btn-secondary flex-1"
            >
              إلغاء
            </button>
          </div>
        </form>
      </Modal>

      <Modal
        isOpen={showNewProjectModal}
        onClose={() => setShowNewProjectModal(false)}
        title="إضافة مشروع جديد"
      >
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم المشروع</label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل اسم المشروع"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">العميل</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <option>أحمد محمد</option>
              <option>فاطمة علي</option>
              <option>محمد سالم</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع المشروع</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <option>تصميم موقع ويب</option>
              <option>تصميم هوية بصرية</option>
              <option>تطبيق جوال</option>
              <option>تصميم جرافيك</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الميزانية</label>
            <input
              type="number"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل الميزانية بالريال"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ التسليم</label>
            <input
              type="date"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              onClick={(e) => {
                e.preventDefault()
                alert('تم إضافة المشروع بنجاح!')
                setShowNewProjectModal(false)
              }}
              className="btn-primary flex-1"
            >
              إضافة المشروع
            </button>
            <button
              type="button"
              onClick={() => setShowNewProjectModal(false)}
              className="btn-secondary flex-1"
            >
              إلغاء
            </button>
          </div>
        </form>
      </Modal>
    </div>
  )
}
