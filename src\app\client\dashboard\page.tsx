'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Modal from '@/components/Modal'
import {
  LayoutDashboard,
  FileText,
  MessageCircle,
  Settings,
  LogOut,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Palette
} from 'lucide-react'

export default function ClientDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard')
  const [showNewProjectModal, setShowNewProjectModal] = useState(false)
  const [showNewTicketModal, setShowNewTicketModal] = useState(false)
  const [showProfileEdit, setShowProfileEdit] = useState(false)
  const router = useRouter()

  // Handle logout
  const handleLogout = () => {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      router.push('/')
    }
  }

  // Handle new project
  const handleNewProject = () => {
    setShowNewProjectModal(true)
  }

  // Handle new support ticket
  const handleNewTicket = () => {
    setShowNewTicketModal(true)
  }

  // Handle project details
  const handleProjectDetails = (projectId: number) => {
    alert(`عرض تفاصيل المشروع رقم ${projectId}`)
  }

  // Handle file download
  const handleDownloadFiles = (projectId: number) => {
    alert(`تحميل ملفات المشروع رقم ${projectId}`)
  }

  // Handle view ticket
  const handleViewTicket = (ticketId: number) => {
    alert(`عرض تذكرة الدعم رقم ${ticketId}`)
  }

  // Handle save profile
  const handleSaveProfile = () => {
    alert('تم حفظ التغييرات بنجاح!')
    setShowProfileEdit(false)
  }

  const projects = [
    { id: 1, name: 'تصميم موقع الشركة', status: 'قيد التنفيذ', progress: 75, dueDate: '2024-01-15' },
    { id: 2, name: 'تصميم الهوية البصرية', status: 'مكتمل', progress: 100, dueDate: '2024-01-10' },
    { id: 3, name: 'تطبيق الجوال', status: 'في الانتظار', progress: 25, dueDate: '2024-02-01' },
  ]

  const supportTickets = [
    { id: 1, subject: 'طلب تعديل على التصميم', status: 'مفتوح', priority: 'عالي', date: '2024-01-12' },
    { id: 2, subject: 'استفسار عن الموعد النهائي', status: 'مغلق', priority: 'متوسط', date: '2024-01-10' },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل': return 'text-green-600 bg-green-100'
      case 'قيد التنفيذ': return 'text-blue-600 bg-blue-100'
      case 'في الانتظار': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالي': return 'text-red-600 bg-red-100'
      case 'متوسط': return 'text-yellow-600 bg-yellow-100'
      case 'منخفض': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex" dir="rtl">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <Palette className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-gray-800">وكالة التصميم</h2>
              <p className="text-sm text-gray-600">لوحة العميل</p>
            </div>
          </div>
        </div>

        <nav className="mt-6">
          <div className="px-4 space-y-2">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'dashboard' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <LayoutDashboard className="w-5 h-5 ml-3" />
              الرئيسية
            </button>
            <button
              onClick={() => setActiveTab('projects')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'projects' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <FileText className="w-5 h-5 ml-3" />
              مشاريعي
            </button>
            <button
              onClick={() => setActiveTab('support')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'support' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <MessageCircle className="w-5 h-5 ml-3" />
              الدعم الفني
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`w-full flex items-center px-4 py-3 text-right rounded-lg transition-colors ${
                activeTab === 'settings' ? 'bg-primary-50 text-primary-600' : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Settings className="w-5 h-5 ml-3" />
              الإعدادات
            </button>
          </div>
        </nav>

        <div className="absolute bottom-4 right-4 left-4">
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-3 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
          >
            <LogOut className="w-5 h-5 ml-3" />
            تسجيل الخروج
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">مرحباً، أحمد محمد</h1>
            <p className="text-gray-600">إليك نظرة عامة على مشاريعك</p>
          </div>
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-white" />
            </div>
          </div>
        </div>

        {/* Dashboard Content */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">إجمالي المشاريع</p>
                    <p className="text-2xl font-bold text-gray-800">3</p>
                  </div>
                  <FileText className="w-8 h-8 text-primary-600" />
                </div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">قيد التنفيذ</p>
                    <p className="text-2xl font-bold text-blue-600">1</p>
                  </div>
                  <Clock className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">مكتملة</p>
                    <p className="text-2xl font-bold text-green-600">1</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </div>

            {/* Recent Projects */}
            <div className="bg-white rounded-xl shadow-sm border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-800">المشاريع الحديثة</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {projects.slice(0, 2).map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-800">{project.name}</h3>
                        <div className="flex items-center mt-2 space-x-4 space-x-reverse">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                            {project.status}
                          </span>
                          <span className="text-sm text-gray-600">موعد التسليم: {project.dueDate}</span>
                        </div>
                      </div>
                      <div className="w-24">
                        <div className="bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary-600 h-2 rounded-full" 
                            style={{ width: `${project.progress}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-600 mt-1 text-center">{project.progress}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Projects Tab */}
        {activeTab === 'projects' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-800">مشاريعي</h2>
              <button
                onClick={handleNewProject}
                className="btn-primary flex items-center"
              >
                <Plus className="w-4 h-4 ml-2" />
                مشروع جديد
              </button>
            </div>

            <div className="grid gap-6">
              {projects.map((project) => (
                <div key={project.id} className="bg-white p-6 rounded-xl shadow-sm border">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{project.name}</h3>
                      <p className="text-gray-600">موعد التسليم: {project.dueDate}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </div>
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>التقدم</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <button
                      onClick={() => handleProjectDetails(project.id)}
                      className="btn-primary text-sm"
                    >
                      عرض التفاصيل
                    </button>
                    <button
                      onClick={() => handleDownloadFiles(project.id)}
                      className="btn-secondary text-sm"
                    >
                      تحميل الملفات
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Support Tab */}
        {activeTab === 'support' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-800">الدعم الفني</h2>
              <button
                onClick={handleNewTicket}
                className="btn-primary flex items-center"
              >
                <Plus className="w-4 h-4 ml-2" />
                تذكرة جديدة
              </button>
            </div>

            <div className="bg-white rounded-xl shadow-sm border">
              <div className="p-6">
                <div className="space-y-4">
                  {supportTickets.map((ticket) => (
                    <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-800">{ticket.subject}</h3>
                        <div className="flex items-center mt-2 space-x-4 space-x-reverse">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            ticket.status === 'مفتوح' ? 'text-green-600 bg-green-100' : 'text-gray-600 bg-gray-100'
                          }`}>
                            {ticket.status}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                            {ticket.priority}
                          </span>
                          <span className="text-sm text-gray-600">{ticket.date}</span>
                        </div>
                      </div>
                      <button
                        onClick={() => handleViewTicket(ticket.id)}
                        className="btn-secondary text-sm"
                      >
                        عرض
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">الإعدادات</h2>
            
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">المعلومات الشخصية</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                  <input type="text" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="أحمد محمد" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                  <input type="email" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="<EMAIL>" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                  <input type="tel" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="+966 50 123 4567" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الشركة</label>
                  <input type="text" className="w-full p-3 border border-gray-300 rounded-lg" defaultValue="شركة التقنية المتقدمة" />
                </div>
              </div>
              <div className="mt-6">
                <button
                  onClick={handleSaveProfile}
                  className="btn-primary"
                >
                  حفظ التغييرات
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <Modal
        isOpen={showNewProjectModal}
        onClose={() => setShowNewProjectModal(false)}
        title="طلب مشروع جديد"
      >
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم المشروع</label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل اسم المشروع"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع المشروع</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <option>تصميم موقع ويب</option>
              <option>تصميم هوية بصرية</option>
              <option>تطبيق جوال</option>
              <option>تصميم جرافيك</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف المشروع</label>
            <textarea
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="اكتب وصفاً مفصلاً للمشروع"
            ></textarea>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الميزانية المتوقعة</label>
            <input
              type="number"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل الميزانية بالريال"
            />
          </div>
          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              onClick={(e) => {
                e.preventDefault()
                alert('تم إرسال طلب المشروع بنجاح!')
                setShowNewProjectModal(false)
              }}
              className="btn-primary flex-1"
            >
              إرسال الطلب
            </button>
            <button
              type="button"
              onClick={() => setShowNewProjectModal(false)}
              className="btn-secondary flex-1"
            >
              إلغاء
            </button>
          </div>
        </form>
      </Modal>

      <Modal
        isOpen={showNewTicketModal}
        onClose={() => setShowNewTicketModal(false)}
        title="تذكرة دعم جديدة"
      >
        <form className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">موضوع التذكرة</label>
            <input
              type="text"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="أدخل موضوع التذكرة"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الأولوية</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <option>منخفض</option>
              <option>متوسط</option>
              <option>عالي</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
            <textarea
              rows={5}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="اكتب وصفاً مفصلاً للمشكلة أو الاستفسار"
            ></textarea>
          </div>
          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              onClick={(e) => {
                e.preventDefault()
                alert('تم إرسال تذكرة الدعم بنجاح!')
                setShowNewTicketModal(false)
              }}
              className="btn-primary flex-1"
            >
              إرسال التذكرة
            </button>
            <button
              type="button"
              onClick={() => setShowNewTicketModal(false)}
              className="btn-secondary flex-1"
            >
              إلغاء
            </button>
          </div>
        </form>
      </Modal>
    </div>
  )
}
